/*
 * BuildBarStockFilter - Epicor Helper Function
 *
 * DESCRIPTION:
 * This helper function builds a description filter string for bar stock based on separate
 * material and shape parameters. It returns a properly formatted filter string that can
 * be passed to the LogBarStockAssignment function for precise filtering.
 *
 * INPUT PARAMETERS:
 * - includeBrass (bool): Include brass materials (360 1/2 Hard)
 * - includeSteel (bool): Include steel materials (1215, 1144, 12L14, 4140, 8620, etc.)
 * - includeStainlessSteel (bool): Include stainless steel materials (303, 304, 316)
 * - includeAluminum (bool): Include aluminum materials (6061 T6, 211 T3, etc.)
 * - includeDelrin (bool): Include Delrin plastic materials
 * - includeCopper (bool): Include copper materials
 * - includeRoundBars (bool): Include round bar shapes
 * - includeHexBars (bool): Include hex bar shapes
 * - includeSquareBars (bool): Include square bar shapes
 * - includeRectangularBars (bool): Include rectangular bar shapes
 * - includeFlatBars (bool): Include flat bar shapes
 * - includeTubes (bool): Include tube shapes (round, square)
 * - includePipes (bool): Include pipe shapes
 * - includePlates (bool): Include plate shapes
 *
 * OUTPUT PARAMETERS:
 * - filterString (string): Generated description filter string for use with LogBarStockAssignment
 *
 * USAGE EXAMPLES:
 *
 * Example 1 - Only brass round bars:
 * Input: includeBrass=true, includeRoundBars=true, all others=false
 * Output: "Bar, Round, Brass"
 *
 * Example 2 - Multiple materials, single shape:
 * Input: includeBrass=true, includeSteel=true, includeRoundBars=true, all others=false
 * Output: "Bar, Round, Brass~Bar, Round, Steel"
 * LogBarStockAssignment matches: Parts with (Bar AND Round AND Brass) OR (Bar AND Round AND Steel)
 *
 * Example 3 - Single material, multiple shapes:
 * Input: includeBrass=true, includeRoundBars=true, includeHexBars=true, all others=false
 * Output: "Bar, Round, Brass~Bar, Hex, Brass"
 *
 * Example 4 - Multiple materials, no shapes:
 * Input: includeBrass=true, includeSteel=true, includeAluminum=true, all others=false
 * Output: "Brass~Steel~Aluminum"
 *
 * Example 5 - All materials and shapes:
 * Input: All parameters=true
 * Output: "" (empty string = no filter, returns all)
 *
 * BEHAVIOR NOTES:
 * • Returns empty string if all parameters are true (no filtering needed)
 * • Returns empty string if all parameters are false (no valid filter)
 * • Returns tilde-delimited (~) list of ALL selected combinations
 * • Each combination is a complete filter term (e.g., "Bar, Round, Brass")
 * • Multiple terms use OR logic: matches ANY of the tilde-separated combinations
 * • Within each combination: ALL comma-delimited terms must be present (AND logic)
 * • Uses tilde (~) delimiter to separate combinations, comma (,) to separate required terms
 * • Generates exact combinations of selected materials and shapes
 *
 * EPICOR FUNCTION REQUIREMENTS:
 * • No using statements (Epicor constraint)
 * • Void return type with output parameter
 * • No member declarations
 *
 * INTEGRATION:
 * Call this function first to build the filter string, then pass the result to
 * LogBarStockAssignment function's descriptionFilter parameter.
 */

// Initialize the filter string
filterString = "";

// Build lists of selected materials and shapes
var selectedMaterials = new System.Collections.Generic.List<string>();
var selectedShapes = new System.Collections.Generic.List<string>();

// Add selected materials (order matters - most specific first to avoid conflicts)
if (includeStainlessSteel) selectedMaterials.Add("Stainless Steel");  // Must come before "Steel"
if (includeSteel) selectedMaterials.Add("Steel");
if (includeBrass) selectedMaterials.Add("Brass");
if (includeAluminum) selectedMaterials.Add("Aluminum");
if (includeDelrin) selectedMaterials.Add("Delrin");
if (includeCopper) selectedMaterials.Add("Copper");

// Add selected shapes
if (includeRoundBars) selectedShapes.Add("Bar, Round");
if (includeHexBars) selectedShapes.Add("Bar, Hex");
if (includeSquareBars) selectedShapes.Add("Bar, Square");
if (includeRectangularBars) selectedShapes.Add("Bar, Rectangular");
if (includeFlatBars) selectedShapes.Add("Bar, Flat");
if (includeTubes) selectedShapes.Add("Tube");
if (includePipes) selectedShapes.Add("Pipe");
if (includePlates) selectedShapes.Add("Plate");

// If nothing is selected, return empty string
if (selectedMaterials.Count == 0 && selectedShapes.Count == 0)
{
    filterString = "";
    return;
}

// If everything is selected, return empty string (no filter needed)
if (selectedMaterials.Count == 6 && selectedShapes.Count == 8)
{
    filterString = "";
    return;
}

// Build exact filter based on selections
// Strategy: Return comma-delimited list of ALL selected criteria

// Case 1: Only materials selected (no shape filter)
if (selectedMaterials.Count > 0 && selectedShapes.Count == 0)
{
    filterString = string.Join("~", selectedMaterials.ToArray());
    return;
}

// Case 2: Only shapes selected (no material filter)
if (selectedShapes.Count > 0 && selectedMaterials.Count == 0)
{
    filterString = string.Join("~", selectedShapes.ToArray());
    return;
}

// Case 3: Both materials and shapes selected
if (selectedMaterials.Count > 0 && selectedShapes.Count > 0)
{
    // Create combinations of each shape with each material
    var combinations = new System.Collections.Generic.List<string>();

    foreach (var shape in selectedShapes)
    {
        foreach (var material in selectedMaterials)
        {
            // Combine shape and material with consistent spacing
            combinations.Add(shape + ", " + material);
        }
    }

    filterString = string.Join("~", combinations.ToArray());
    return;
}

// Fallback - should not reach here, but just in case
filterString = "";
