/*
 * LogBarStockAssignment - Epicor Function
 *
 * DESCRIPTION:
 * This function returns a comprehensive dataset of bar stock assignments to jobs for logging purposes.
 * It tracks when parts are assigned to jobs and operations, capturing timestamp, user, part details, job information,
 * and quantities for audit and analysis purposes. Includes any transaction type that assigns materials to jobs.
 *
 * INPUT PARAMETERS:
 * - startDate (System.DateTime?): Optional start date for the assignment log query (inclusive). Use null to include all dates
 * - endDate (System.DateTime?): Optional end date for the assignment log query (inclusive). Use null to include all dates
 * - jobNumber (string): Optional job number filter. Use empty string "" to include all jobs
 * - partNumber (string): Optional part number filter. Use empty string "" to include all parts
 * - descriptionFilter (string): Optional part description filter. Use empty string "" to include all parts.
 *                               Supports tilde-delimited (~) filter groups with comma-delimited AND terms within each group.
 *                               Example: "Bar,Round,Brass~Bar,Hex,Steel" matches parts containing ALL of (Bar AND Round AND Brass)
 *                               OR ALL of (Bar AND Hex AND Steel)
 *
 * OUTPUT PARAMETERS:
 * - outputDataSet (System.Data.DataSet): Contains bar stock assignment log data
 *
 * DATASET STRUCTURE:
 *
 * Table: "BarStockAssignments" - Contains detailed bar stock assignment records
 * ┌─────────────────┬──────────┬─────────────────────────────────────────┐
 * │ Column Name     │ Data Type│ Description                             │
 * ├─────────────────┼──────────┼─────────────────────────────────────────┤
 * │ AssignmentDate  │ DateTime │ Date and time when assignment occurred  │
 * │ UserID          │ string   │ User who performed the assignment       │
 * │ PartNumber      │ string   │ Part number of the bar stock            │
 * │ PartDescription │ string   │ Description of the part                 │
 * │ JobNumber       │ string   │ Job number receiving the assignment     │
 * │ JobOperation    │ int      │ Job operation sequence number           │
 * │ OperationDesc   │ string   │ Description of the job operation        │
 * │ AssignedQty     │ decimal  │ Quantity of bar stock assigned          │
 * │ UnitOfMeasure   │ string   │ Unit of measure for the quantity        │
 * │ TransactionType │ string   │ Type of transaction (STK-MTL, WIP-WIP, etc.) │
 * │ TransactionNum  │ Guid     │ Unique transaction identifier           │
 * │ JobPartNumber   │ string   │ Part number being produced by the job   │
 * │ JobDescription  │ string   │ Description of job/part being produced  │
 * │ RequiredQty     │ decimal  │ Total quantity required for job         │
 * │ IssuedQty       │ decimal  │ Total quantity issued to job so far     │
 * └─────────────────┴──────────┴─────────────────────────────────────────┘
 *
 * USAGE EXAMPLES:
 *
 * Example 1 - Get all assignments for today:
 * Input: startDate = DateTime.Today, endDate = DateTime.Today.AddDays(1), jobNumber = "", partNumber = ""
 * Result: Returns all bar stock assignments made today
 *
 * Example 2 - Get assignments for specific job (all dates):
 * Input: startDate = null, endDate = null, jobNumber = "JOB001", partNumber = ""
 * Result: Returns all assignments to job JOB001 regardless of date
 *
 * Example 3 - Get assignments for specific part in date range:
 * Input: startDate = DateTime.Today.AddDays(-7), endDate = DateTime.Today, jobNumber = "", partNumber = "BAR-123"
 * Result: Returns all assignments of part BAR-123 in the last 7 days
 *
 * Example 4 - Get all assignments (no filters):
 * Input: startDate = null, endDate = null, jobNumber = "", partNumber = "", descriptionFilter = ""
 * Result: Returns all bar stock assignments in the system
 *
 * Example 5 - Filter by single material type:
 * Input: startDate = null, endDate = null, jobNumber = "", partNumber = "", descriptionFilter = "Brass"
 * Result: Returns all assignments of brass parts
 *
 * Example 6 - Filter by multiple materials (OR logic):
 * Input: startDate = null, endDate = null, jobNumber = "", partNumber = "", descriptionFilter = "Brass~Steel~Aluminum"
 * Result: Returns all assignments of brass, steel, or aluminum parts
 *
 * Example 7 - Filter by specific combinations (AND within groups, OR between groups):
 * Input: startDate = null, endDate = null, jobNumber = "", partNumber = "", descriptionFilter = "Bar,Round,Brass~Bar,Hex,Steel"
 * Result: Returns assignments of parts that contain ALL of (Bar AND Round AND Brass) OR ALL of (Bar AND Hex AND Steel)
 *
 * Example 8 - Complex filtering with multiple requirements:
 * Input: startDate = null, endDate = null, jobNumber = "", partNumber = "", descriptionFilter = "Round,Brass~Hex,Steel,Stainless"
 * Result: Returns parts containing (Round AND Brass) OR (Hex AND Steel AND Stainless)
 *
 * BEHAVIOR NOTES:
 * • Only includes stock materials: PartNum begins with "0" AND ClassID = 'RawM'
 * • Includes any transaction type assigned to jobs and operations (not limited to STK-MTL)
 * • Includes both completed and partial assignments
 * • All string fields are null-safe (empty string returned if null)
 * • Date range is inclusive on both start and end dates (when provided)
 * • Null start/end dates disable date filtering (returns all dates)
 * • Empty job/part/description filters include all records
 * • Description filter uses case-insensitive partial matching (contains)
 * • Description filter supports tilde-delimited (~) filter groups with comma-delimited terms within groups
 * • Within each group: ALL comma-delimited terms must be present (AND logic)
 * • Between groups: ANY group can match (OR logic)
 * • Example: "Brass~Steel" matches parts containing "Brass" OR "Steel" (single terms)
 * • Example: "Bar,Round,Brass~Hex,Steel" matches parts containing (Bar AND Round AND Brass) OR (Hex AND Steel)
 *
 * COMMON DESCRIPTION FILTER EXAMPLES:
 * • "Bar" - All bar stock (excludes tubes, pipes, plates)
 * • "Brass" - All brass materials
 * • "Steel" - All steel materials (includes stainless)
 * • "Stainless Steel" - Only stainless steel materials
 * • "Aluminum" - All aluminum materials
 * • "Bar,Round" - Only round bars (any material)
 * • "Bar,Hex" - Only hex bars (any material)
 * • "Bar,Square" - Only square bars (any material)
 * • "Bar,Round,Brass" - Only parts containing ALL of: Bar AND Round AND Brass
 * • "Tube" - All tubes (excludes bars, pipes, plates)
 * • "Delrin" - All Delrin plastic materials
 * • "Round,Brass~Hex,Steel" - Parts with (Round AND Brass) OR (Hex AND Steel)
 * • "Bar,Round~Bar,Hex~Bar,Square" - Any bar that is Round OR Hex OR Square
 *
 * EPICOR FUNCTION REQUIREMENTS:
 * • No using statements (Epicor constraint)
 * • Void return type with output parameter
 * • No member declarations
 * • Uses Db.PartTran, Db.JobMtl, Db.JobHead, Db.JobOper, and Db.Part for data access
 *
 * INTEGRATION:
 * This function can be called from Epicor BPMs, Method Directives, or other custom functions
 * to retrieve comprehensive bar stock assignment logs for audit, analysis, and reporting purposes.
 */

// Create DataSet to hold bar stock assignment information
var dataSet = new System.Data.DataSet("BarStockAssignmentData");

// Create BarStockAssignments DataTable
var assignmentsTable = new System.Data.DataTable("BarStockAssignments");
assignmentsTable.Columns.Add("AssignmentDate", typeof(System.DateTime));
assignmentsTable.Columns.Add("UserID", typeof(string));
assignmentsTable.Columns.Add("PartNumber", typeof(string));
assignmentsTable.Columns.Add("PartDescription", typeof(string));
assignmentsTable.Columns.Add("JobNumber", typeof(string));
assignmentsTable.Columns.Add("JobOperation", typeof(int));
assignmentsTable.Columns.Add("OperationDesc", typeof(string));
assignmentsTable.Columns.Add("AssignedQty", typeof(decimal));
assignmentsTable.Columns.Add("UnitOfMeasure", typeof(string));
assignmentsTable.Columns.Add("TransactionType", typeof(string));
assignmentsTable.Columns.Add("TransactionNum", typeof(System.Guid));
assignmentsTable.Columns.Add("JobPartNumber", typeof(string));
assignmentsTable.Columns.Add("JobDescription", typeof(string));
assignmentsTable.Columns.Add("RequiredQty", typeof(decimal));
assignmentsTable.Columns.Add("IssuedQty", typeof(decimal));

// Create Debug DataTable
var debugTable = new System.Data.DataTable("Debug");
debugTable.Columns.Add("DebugCategory", typeof(string));
debugTable.Columns.Add("DebugValue", typeof(string));
debugTable.Columns.Add("DebugDescription", typeof(string));

// Add tables to dataset
dataSet.Tables.Add(assignmentsTable);
dataSet.Tables.Add(debugTable);

// Add essential debug information
debugTable.Rows.Add("Parameters", "Filters Applied", "StartDate: " + (startDate.HasValue ? startDate.Value.ToString("yyyy-MM-dd") : "All") +
                   ", EndDate: " + (endDate.HasValue ? endDate.Value.ToString("yyyy-MM-dd") : "All") +
                   ", Job: " + (string.IsNullOrEmpty(jobNumber) ? "All" : jobNumber) +
                   ", Part: " + (string.IsNullOrEmpty(partNumber) ? "All" : partNumber) +
                   ", Description: " + (string.IsNullOrEmpty(descriptionFilter) ? "All" : descriptionFilter));

// Validate date range (only if both dates are provided)
if (startDate.HasValue && endDate.HasValue && startDate.Value > endDate.Value)
{
    debugTable.Rows.Add("Error", "Invalid Date Range", "StartDate (" + startDate.ToString() + ") is after EndDate (" + endDate.ToString() + ")");
    // Return dataset with debug info if invalid date range
    outputDataSet = dataSet;
    return;
}

// Parse description filter for multiple filter groups using tilde delimiter (C# 7.2 compatible)
// Each group can contain comma-delimited terms that must ALL be present (AND logic within group)
// Groups are separated by tilde and use OR logic between groups
var descriptionFilterGroups = new string[0][];
var useDescriptionFilter = false;
if (!string.IsNullOrEmpty(descriptionFilter))
{
    var filterGroups = descriptionFilter.Split(new char[] { '~' });
    descriptionFilterGroups = new string[filterGroups.Length][];

    for (int i = 0; i < filterGroups.Length; i++)
    {
        var group = filterGroups[i].Trim();
        if (!string.IsNullOrEmpty(group))
        {
            // Split each group by comma to get individual terms that must ALL be present
            var terms = group.Split(new char[] { ',' });
            descriptionFilterGroups[i] = new string[terms.Length];

            for (int j = 0; j < terms.Length; j++)
            {
                descriptionFilterGroups[i][j] = terms[j].Trim().ToLower();
            }
        }
        else
        {
            descriptionFilterGroups[i] = new string[0];
        }
    }
    useDescriptionFilter = true;
    debugTable.Rows.Add("Filter Processing", "Description Filter Groups", "Parsed " + filterGroups.Length.ToString() + " filter groups using tilde delimiter, each with comma-delimited AND terms");
}
else
{
    debugTable.Rows.Add("Filter Processing", "No Description Filter", "No description filtering applied");
}

// Build the query to get bar stock assignments
// Join PartTran (transactions) with JobMtl (job materials), JobHead (job info), JobOper (operations), and Part (part info)
var query = from pt in Db.PartTran
            join jm in Db.JobMtl on new { pt.Company, pt.JobNum, MtlSeq = pt.JobSeq } equals new { jm.Company, jm.JobNum, jm.MtlSeq }
            join jh in Db.JobHead on new { jm.Company, jm.JobNum } equals new { jh.Company, jh.JobNum }
            join jo in Db.JobOper on new { jm.Company, jm.JobNum, OprSeq = jm.RelatedOperation } equals new { jo.Company, jo.JobNum, jo.OprSeq } into joGroup
            from jo in joGroup.DefaultIfEmpty()
            join p in Db.Part on new { pt.Company, pt.PartNum } equals new { p.Company, p.PartNum } into pGroup
            from p in pGroup.DefaultIfEmpty()
            where pt.TranDate.HasValue &&
                  (!startDate.HasValue || pt.TranDate.Value >= startDate.Value) &&
                  (!endDate.HasValue || pt.TranDate.Value <= endDate.Value) &&
                  !string.IsNullOrEmpty(pt.JobNum) &&
                  pt.PartNum.StartsWith("0") &&  // Only parts beginning with "0"
                  (p != null && p.ClassID == "RawM") &&  // Only raw material class parts
                  (string.IsNullOrEmpty(jobNumber) || pt.JobNum == jobNumber) &&
                  (string.IsNullOrEmpty(partNumber) || pt.PartNum == partNumber)
            orderby pt.TranDate descending, pt.SysTime descending
            select new
            {
                AssignmentDate = pt.TranDate.Value,
                UserID = pt.EntryPerson ?? "",
                PartNumber = pt.PartNum ?? "",
                PartDescription = p != null ? (p.PartDescription ?? "") : "",
                JobNumber = pt.JobNum ?? "",
                JobOperation = jm.MtlSeq,
                OperationDesc = jo != null ? (jo.OpDesc ?? "") : "",
                AssignedQty = pt.TranQty,
                UnitOfMeasure = pt.UM ?? "",
                TransactionType = pt.TranType ?? "",
                TransactionNum = pt.SysRowID,
                JobPartNumber = jh.PartNum ?? "",
                JobDescription = jh.PartDescription ?? "",
                RequiredQty = jm.RequiredQty,
                IssuedQty = jm.IssuedQty
            };

// Execute query and apply description filtering
try
{
    var allAssignments = query.ToList();

    // Count all assignments manually to avoid C# 7.2 issues
    var allAssignmentCount = 0;
    foreach (var item in allAssignments)
    {
        allAssignmentCount++;
    }

    debugTable.Rows.Add("Query", "Initial Results", "Query returned " + allAssignmentCount.ToString() + " records before description filtering");

    // Debug: Track transaction types found
    var transactionTypeCounts = new System.Collections.Generic.Dictionary<string, int>();
    foreach (var assignment in allAssignments)
    {
        var tranType = assignment.TransactionType ?? "NULL";
        if (transactionTypeCounts.ContainsKey(tranType))
        {
            transactionTypeCounts[tranType]++;
        }
        else
        {
            transactionTypeCounts[tranType] = 1;
        }
    }

    foreach (var kvp in transactionTypeCounts)
    {
        debugTable.Rows.Add("TransactionTypes", kvp.Key, "Found " + kvp.Value.ToString() + " transactions of this type");
    }

    // Apply description filtering post-query using direct property access
    var filteredAssignments = new System.Collections.Generic.List<dynamic>();
    if (useDescriptionFilter)
    {
        foreach (var assignment in allAssignments)
        {
            // Get part description directly (no reflection needed)
            string partDesc = (assignment.PartDescription ?? "").ToLower();
            var matches = false;

            // Check each filter group (OR logic between groups)
            for (int groupIndex = 0; groupIndex < descriptionFilterGroups.Length; groupIndex++)
            {
                var filterGroup = descriptionFilterGroups[groupIndex];
                if (filterGroup == null || filterGroup.Length == 0)
                    continue;

                // Check if ALL terms in this group are present (AND logic within group)
                var allTermsMatch = true;
                for (int termIndex = 0; termIndex < filterGroup.Length; termIndex++)
                {
                    var term = filterGroup[termIndex];
                    var termMatches = false;

                    if (term == "steel")
                    {
                        // Special handling: only match "steel" if it's NOT "stainless steel"
                        termMatches = partDesc.Contains("steel") && !partDesc.Contains("stainless steel");
                    }
                    else
                    {
                        // Normal contains matching for all other terms
                        termMatches = partDesc.Contains(term);
                    }

                    if (!termMatches)
                    {
                        allTermsMatch = false;
                        break; // This group fails, no need to check remaining terms
                    }
                }

                if (allTermsMatch)
                {
                    matches = true;
                    break; // Found a matching group, no need to check remaining groups
                }
            }

            if (matches)
            {
                filteredAssignments.Add(assignment);
            }
        }
    }
    else
    {
        // No filtering - add all assignments
        foreach (var assignment in allAssignments)
        {
            filteredAssignments.Add(assignment);
        }
    }

    // Count filtered assignments manually
    var assignmentCount = 0;
    foreach (var item in filteredAssignments)
    {
        assignmentCount++;
    }
    debugTable.Rows.Add("Results", "After Description Filter", "Filtered to " + assignmentCount.ToString() + " records");

    foreach (var assignment in filteredAssignments)
    {
        var row = assignmentsTable.NewRow();

        // Direct property access (works with anonymous types in Epicor)
        row["AssignmentDate"] = assignment.AssignmentDate;
        row["UserID"] = assignment.UserID;
        row["PartNumber"] = assignment.PartNumber;
        row["PartDescription"] = assignment.PartDescription;
        row["JobNumber"] = assignment.JobNumber;
        row["JobOperation"] = assignment.JobOperation;
        row["OperationDesc"] = assignment.OperationDesc;
        row["AssignedQty"] = assignment.AssignedQty;
        row["UnitOfMeasure"] = assignment.UnitOfMeasure;

        row["TransactionType"] = assignment.TransactionType;
        row["TransactionNum"] = assignment.TransactionNum;
        row["JobPartNumber"] = assignment.JobPartNumber;
        row["JobDescription"] = assignment.JobDescription;
        row["RequiredQty"] = assignment.RequiredQty;
        row["IssuedQty"] = assignment.IssuedQty;

        assignmentsTable.Rows.Add(row);
    }
    // Processing complete
}
catch (System.Exception ex)
{
    debugTable.Rows.Add("Error", "Query Failed", "Query execution failed: " + ex.Message);
    debugTable.Rows.Add("Error", "Exception Type", "Exception occurred");
    if (ex.InnerException != null)
    {
        debugTable.Rows.Add("Error", "Inner Exception", ex.InnerException.Message);
    }
    // Return dataset with debug info on query failure
    outputDataSet = dataSet;
    return;
}

// Final summary
debugTable.Rows.Add("Summary", "Execution Complete", "Successfully returned " + assignmentsTable.Rows.Count.ToString() + " bar stock assignment records");

// Return the populated dataset
outputDataSet = dataSet;
