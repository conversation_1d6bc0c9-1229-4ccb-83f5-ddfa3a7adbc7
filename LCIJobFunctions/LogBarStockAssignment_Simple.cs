/*
 * LogBarStockAssignment_Simple - Epicor Function (C# 7.2 Compatible)
 *
 * DESCRIPTION:
 * Simplified version that returns bar stock assignments without complex filtering.
 * This version avoids all C# 7.2 compatibility issues and reflection problems.
 *
 * INPUT PARAMETERS:
 * - startDate (System.DateTime?): Optional start date for the assignment log query (inclusive). Use null to include all dates
 * - endDate (System.DateTime?): Optional end date for the assignment log query (inclusive). Use null to include all dates
 * - jobNumber (string): Optional job number filter. Use empty string "" to include all jobs
 * - partNumber (string): Optional part number filter. Use empty string "" to include all parts
 *
 * OUTPUT PARAMETERS:
 * - outputDataSet (System.Data.DataSet): Contains bar stock assignment log data
 *
 * BEHAVIOR NOTES:
 * • Only includes stock materials: PartNum begins with "0" AND ClassID = 'RawM'
 * • Filters on STK-MTL transaction type (stock to material/job assignments)
 * • Includes both completed and partial assignments
 * • All string fields are null-safe (empty string returned if null)
 * • Date range is inclusive on both start and end dates (when provided)
 * • Null start/end dates disable date filtering (returns all dates)
 * • Empty job/part filters include all records
 */

// Create DataSet to hold bar stock assignment information
var dataSet = new System.Data.DataSet("BarStockAssignmentData");

// Create BarStockAssignments DataTable
var assignmentsTable = new System.Data.DataTable("BarStockAssignments");
assignmentsTable.Columns.Add("AssignmentDate", typeof(System.DateTime));
assignmentsTable.Columns.Add("UserID", typeof(string));
assignmentsTable.Columns.Add("PartNumber", typeof(string));
assignmentsTable.Columns.Add("PartDescription", typeof(string));
assignmentsTable.Columns.Add("JobNumber", typeof(string));
assignmentsTable.Columns.Add("JobOperation", typeof(int));
assignmentsTable.Columns.Add("OperationDesc", typeof(string));
assignmentsTable.Columns.Add("AssignedQty", typeof(decimal));
assignmentsTable.Columns.Add("UnitOfMeasure", typeof(string));
assignmentsTable.Columns.Add("TransactionType", typeof(string));
assignmentsTable.Columns.Add("TransactionNum", typeof(System.Guid));
assignmentsTable.Columns.Add("JobPartNumber", typeof(string));
assignmentsTable.Columns.Add("JobDescription", typeof(string));
assignmentsTable.Columns.Add("RequiredQty", typeof(decimal));
assignmentsTable.Columns.Add("IssuedQty", typeof(decimal));

// Create Debug DataTable
var debugTable = new System.Data.DataTable("Debug");
debugTable.Columns.Add("DebugCategory", typeof(string));
debugTable.Columns.Add("DebugValue", typeof(string));
debugTable.Columns.Add("DebugDescription", typeof(string));

// Add tables to dataset
dataSet.Tables.Add(assignmentsTable);
dataSet.Tables.Add(debugTable);

// Add essential debug information
debugTable.Rows.Add("Parameters", "Filters Applied", "StartDate: " + (startDate.HasValue ? startDate.Value.ToString("yyyy-MM-dd") : "All") +
                   ", EndDate: " + (endDate.HasValue ? endDate.Value.ToString("yyyy-MM-dd") : "All") +
                   ", Job: " + (string.IsNullOrEmpty(jobNumber) ? "All" : jobNumber) +
                   ", Part: " + (string.IsNullOrEmpty(partNumber) ? "All" : partNumber));

// Validate date range (only if both dates are provided)
if (startDate.HasValue && endDate.HasValue && startDate.Value > endDate.Value)
{
    debugTable.Rows.Add("Error", "Invalid Date Range", "StartDate (" + startDate.ToString() + ") is after EndDate (" + endDate.ToString() + ")");
    // Return dataset with debug info if invalid date range
    outputDataSet = dataSet;
    return;
}

// Remove this debug line

// Build the query to get bar stock assignments
var query = from pt in Db.PartTran
            join jm in Db.JobMtl on new { pt.Company, pt.JobNum, MtlSeq = pt.JobSeq } equals new { jm.Company, jm.JobNum, jm.MtlSeq }
            join jh in Db.JobHead on new { jm.Company, jm.JobNum } equals new { jh.Company, jh.JobNum }
            join jo in Db.JobOper on new { jm.Company, jm.JobNum, OprSeq = jm.RelatedOperation } equals new { jo.Company, jo.JobNum, jo.OprSeq } into joGroup
            from jo in joGroup.DefaultIfEmpty()
            join p in Db.Part on new { pt.Company, pt.PartNum } equals new { p.Company, p.PartNum } into pGroup
            from p in pGroup.DefaultIfEmpty()
            where pt.TranType == "STK-MTL" &&  // Stock to Material transactions (assignments to jobs)
                  pt.TranDate.HasValue &&
                  (!startDate.HasValue || pt.TranDate.Value >= startDate.Value) &&
                  (!endDate.HasValue || pt.TranDate.Value <= endDate.Value) &&
                  !string.IsNullOrEmpty(pt.JobNum) &&
                  pt.PartNum.StartsWith("0") &&  // Only parts beginning with "0"
                  (p != null && p.ClassID == "RawM") &&  // Only raw material class parts
                  (string.IsNullOrEmpty(jobNumber) || pt.JobNum == jobNumber) &&
                  (string.IsNullOrEmpty(partNumber) || pt.PartNum == partNumber)
            orderby pt.TranDate descending, pt.SysTime descending
            select new
            {
                AssignmentDate = pt.TranDate.Value,
                UserID = pt.EntryPerson ?? "",
                PartNumber = pt.PartNum ?? "",
                PartDescription = p != null ? (p.PartDescription ?? "") : "",
                JobNumber = pt.JobNum ?? "",
                JobOperation = jm.MtlSeq,
                OperationDesc = jo != null ? (jo.OpDesc ?? "") : "",
                AssignedQty = pt.TranQty,
                UnitOfMeasure = pt.UM ?? "",
                TransactionType = pt.TranType ?? "",
                TransactionNum = pt.SysRowID,
                JobPartNumber = jh.PartNum ?? "",
                JobDescription = jh.PartDescription ?? "",
                RequiredQty = jm.RequiredQty,
                IssuedQty = jm.IssuedQty
            };

// Remove this debug line

try
{
    var assignments = query.ToList();
    
    // Count assignments manually to avoid C# 7.2 issues
    var assignmentCount = 0;
    foreach (var item in assignments)
    {
        assignmentCount++;
    }
    
    debugTable.Rows.Add("Results", "Records Found", "Found " + assignmentCount.ToString() + " bar stock assignments");

    foreach (var assignment in assignments)
    {
        var row = assignmentsTable.NewRow();
        
        // Direct property access (works with anonymous types in Epicor)
        row["AssignmentDate"] = assignment.AssignmentDate;
        row["UserID"] = assignment.UserID;
        row["PartNumber"] = assignment.PartNumber;
        row["PartDescription"] = assignment.PartDescription;
        row["JobNumber"] = assignment.JobNumber;
        row["JobOperation"] = assignment.JobOperation;
        row["OperationDesc"] = assignment.OperationDesc;
        row["AssignedQty"] = assignment.AssignedQty;
        row["UnitOfMeasure"] = assignment.UnitOfMeasure;
        row["TransactionType"] = assignment.TransactionType;
        row["TransactionNum"] = assignment.TransactionNum;
        row["JobPartNumber"] = assignment.JobPartNumber;
        row["JobDescription"] = assignment.JobDescription;
        row["RequiredQty"] = assignment.RequiredQty;
        row["IssuedQty"] = assignment.IssuedQty;
        
        assignmentsTable.Rows.Add(row);
    }
    
    // Remove this debug line
}
catch (System.Exception ex)
{
    debugTable.Rows.Add("Error", "Query Failed", "Query execution failed: " + ex.Message);
    debugTable.Rows.Add("Error", "Exception Type", "Exception occurred");
    if (ex.InnerException != null)
    {
        debugTable.Rows.Add("Error", "Inner Exception", ex.InnerException.Message);
    }
}

// Final summary
debugTable.Rows.Add("Summary", "Execution Complete", "Successfully returned " + assignmentsTable.Rows.Count.ToString() + " bar stock assignment records");

// Return the populated dataset
outputDataSet = dataSet;
